import TIM from 'tim-js-sdk'
import TIMUploadPlugin from 'tim-upload-plugin'

class IMManager {
	constructor() {
		this.tim = null
		this.isLoggedIn = false
		this.isSDKReady = false // SDK是否准备就绪
		this.currentUserID = ''
		this.messageCallbacks = []
		this.groupCallbacks = []
		this.loginSuccessCallbacks = [] // 登录成功回调列表
		this.readyCallbacks = [] // SDK ready回调列表

	}
	onNewMessage(){}
	/**
	 * 初始化IM SDK
	 * @param {Object} config - 配置信息
	 * @param {number} config.SDKAppID - 腾讯云IM应用ID
	 */
	init(config) {
		try {
			console.log('开始初始化IM SDK，配置:', config)

			// 如果已经初始化过，先销毁之前的实例
			if (this.tim) {
				console.log('检测到已存在的TIM实例，先进行清理...')
				try {
					this.tim.destroy()
				} catch (destroyError) {
					console.warn('销毁TIM实例时出错:', destroyError)
				}
				this.tim = null
			}

			// 检查SDKAppID
			if (!config.SDKAppID) {
				throw new Error('SDKAppID 不能为空')
			}

			// 创建TIM实例
			this.tim = TIM.create({
				SDKAppID: config.SDKAppID
			})

			console.log('TIM实例创建成功')

			// 注册腾讯云IM上传插件
			this.tim.registerPlugin({
				'tim-upload-plugin': TIMUploadPlugin
			})

			console.log('IM上传插件注册成功')

			// 监听事件
			this.bindEvents()

			console.log('IM SDK 初始化成功，SDKAppID:', config.SDKAppID)
			return true
		} catch (error) {
			console.error('IM SDK 初始化失败:', error)
			return false
		}
	}

	/**
	 * 绑定事件监听
	 */
	bindEvents() {
		// 监听SDK进入ready状态
		this.tim.on(TIM.EVENT.SDK_READY, this.onSDKReady.bind(this))

		// 监听SDK进入not ready状态
		this.tim.on(TIM.EVENT.SDK_NOT_READY, this.onSDKNotReady.bind(this))

		// 监听被踢下线
		this.tim.on(TIM.EVENT.KICKED_OUT, this.onKickedOut.bind(this))

		// 监听网络状态变化
		this.tim.on(TIM.EVENT.NET_STATE_CHANGE, this.onNetStateChange.bind(this))

		// 监听收到新消息
		this.tim.on(TIM.EVENT.MESSAGE_RECEIVED, this.onMessageReceived.bind(this))

		// 监听群组列表更新
		this.tim.on(TIM.EVENT.GROUP_LIST_UPDATED, this.onGroupListUpdated.bind(this))

		// 监听群组属性更新
		this.tim.on(TIM.EVENT.GROUP_ATTRIBUTES_UPDATED, this.onGroupAttributesUpdated.bind(this))
	}

	/**
	 * SDK Ready 事件处理
	 */
	onSDKReady() {
		console.log('IM SDK Ready - 可以正常使用IM功能')
		this.isSDKReady = true

		// 触发SDK ready回调
		this.triggerReadyCallbacks()
	}

	/**
	 * SDK Not Ready 事件处理
	 */
	onSDKNotReady() {
		console.log('IM SDK Not Ready - IM功能暂时不可用')
		this.isSDKReady = false
	}

	/**
	 * 被踢下线事件处理
	 */
	onKickedOut(event) {
		console.log('被踢下线:', event.data.type)
		this.isLoggedIn = false

		// 显示用户友好的提示信息
		let message = '您的账号在其他设备登录'
		switch(event.data.type) {
			case 'TIM.TYPES.KICKED_OUT_MULT_ACCOUNT':
				message = '您的账号在其他设备登录，已被强制下线'
				break
			case 'TIM.TYPES.KICKED_OUT_MULT_DEVICE':
				message = '您的账号在其他设备登录，已被强制下线'
				break
			case 'TIM.TYPES.KICKED_OUT_USERSIG_EXPIRED':
				message = '登录凭证已过期，请重新登录'
				break
			default:
				message = '您已被强制下线，请重新登录'
		}

		// 使用uni-app的提示框
		if (typeof uni !== 'undefined') {
			uni.showModal({
				title: '提示',
				content: message,
				showCancel: false,
				confirmText: '重新登录',
				success: () => {
					// 跳转到登录页面
					uni.reLaunch({
						url: '/pages/profile/profile'
					})
				}
			})
		}
	}

	/**
	 * 网络状态变化事件处理
	 */
	onNetStateChange(event) {
		console.log('网络状态变化:', event.data.state)

		// 根据网络状态显示相应提示
		if (typeof uni !== 'undefined') {
			switch(event.data.state) {
				case 'TIM.TYPES.NET_STATE_CONNECTED':
					// 网络已连接，可以隐藏之前的断网提示
					console.log('IM网络已连接')
					break
				case 'TIM.TYPES.NET_STATE_CONNECTING':
					console.log('IM网络连接中...')
					break
				case 'TIM.TYPES.NET_STATE_DISCONNECTED':
					uni.showToast({
						title: '网络连接已断开',
						icon: 'none',
						duration: 2000
					})
					break
			}
		}
	}



	changeMessage(message){
		// console.log("@@@@@@@@@@@",,message);
		this.onNewMessage(message)
	}
	/**
	 * 收到新消息事件处理
	 */
	onMessageReceived(event) {
		console.log('收到新消息:', event.data)
		// 确保event.data是消息对象而不是函数
		if (typeof event.data !== 'function') {
			// 如果messageCallbacks为空，添加一个默认回调
			if (this.messageCallbacks.length === 0) {
				this.messageCallbacks.push((message) => {
					// 默认回调逻辑：将消息添加到聊天列表
					console.log('默认回调：将消息添加到聊天列表', message)
					// 这里可以调用页面方法，将消息添加到聊天区域
					if (typeof window.addMessageToChat === 'function') {
						window.addMessageToChat(message)
					}
				})
			}

			// 过滤掉系统消息，只对用户消息触发更新
			const messages = event.data || []
			const userMessages = messages.filter(msg => {
				// 过滤掉系统消息和群提示消息
				return msg.type !== 'TIMGroupTipElem' &&
					   msg.type !== 'TIMGroupSystemNoticeElem' &&
					   !(msg.payload && msg.payload.operationType)
			})

			// 只有当有用户消息时才触发更新
			if (userMessages.length > 0) {
				this.changeMessage(userMessages);
			}

			// 通知所有注册的消息回调
			// this.messageCallbacks.forEach(callback => {
			// 	try {
			// 		if (typeof callback === 'function') {
			// 			callback(event.data)
			// 		}
			// 	} catch (error) {
			// 		console.error('消息回调执行失败:', error)
			// 	}
			// })
		}
	}

	/**
	 * 群组列表更新事件处理
	 */
	onGroupListUpdated(event) {
		console.log('群组列表更新:', event.data)
		// 通知所有注册的群组回调
		this.groupCallbacks.forEach(callback => {
			try {
				callback(event.data)
			} catch (error) {
				console.error('群组回调执行失败:', error)
			}
		})
	}

	/**
	 * 群组属性更新事件处理
	 */
	onGroupAttributesUpdated(event) {
		console.log('群组属性更新:', event.data)
	}

	/**
	 * 登录IM
	 * @param {string} userID - 用户ID
	 * @param {string} userSig - 用户签名
	 */
	async login(userID, userSig) {
		try {
			console.log('开始IM登录，userID:', userID, 'userSig长度:', userSig ? userSig.length : 0)
			console.log( "this.tim",this.tim);
			
			if (!this.tim) {
				throw new Error('IM SDK 未初始化')
			}

			// 检查TIM SDK状态
			console.log('userIDAnduserSig:', userID+'|'+userSig)

			const response = await this.tim.login({
				userID: userID,
				userSig: userSig
			})
			
			console.log('IM登录响应:', response)

			if (response.code === 0) {
				this.currentUserID = userID
				this.isLoggedIn = true  // 设置登录状态为true
				console.log('IM 登录成功，当前状态:', {
					isLoggedIn: this.isLoggedIn,
					currentUserID: this.currentUserID
				})

				// 触发登录成功回调
				this.triggerLoginSuccessCallbacks()

				return { success: true, data: response.data }
			} else {
				console.error('IM 登录失败:', response)

				// 根据错误码提供用户友好的错误信息
				let errorMessage = 'IM登录失败'
				switch(response.code) {
					case 70001:
						errorMessage = '用户签名已过期，请重新登录'
						break
					case 70003:
						errorMessage = '用户签名验证失败'
						break
					case 70009:
						errorMessage = '用户签名验证失败，请重新登录'
						break
					case 70013:
						errorMessage = '用户ID不合法'
						break
					default:
						errorMessage = `IM登录失败: ${response.message || '未知错误'}`
				}

				return { success: false, error: response, message: errorMessage }
			}
		} catch (error) {
			console.error('IM 登录异常:', error)
			return { success: false, error: error, message: 'IM登录异常，请检查网络连接' }
		}
	}

	/**
	 * 登出IM
	 */
	async logout() {
		try {
			if (!this.tim) {
				return { success: true }
			}

			const response = await this.tim.logout()
			this.isLoggedIn = false
			this.currentUserID = ''
			console.log('IM 登出成功')
			return { success: true, data: response.data }
		} catch (error) {
			console.error('IM 登出失败:', error)
			return { success: false, error: error }
		}
	}

	/**
	 * 加入群组
	 * @param {string} groupID - 群组ID
	 */
	async joinGroup(groupID) {
		try {
			if (!this.tim || !this.isLoggedIn || !this.isSDKReady) {
				throw new Error('IM 未登录或SDK未准备就绪')
			}

			const response = await this.tim.joinGroup({
				groupID: groupID,
				type: TIM.TYPES.GRP_PUBLIC
			})

			if (response.code === 0) {
				console.log('加入群组成功:', groupID)
				return { success: true, data: response.data }
			} else {
				console.error('加入群组失败:', response)

				// 根据错误码提供用户友好的错误信息
				let errorMessage = '加入群聊失败'
				switch(response.code) {
					case 10007:
						errorMessage = '您没有权限加入此群聊'
						break
					case 10010:
						errorMessage = '您已经是群成员'
						break
					case 10015:
						errorMessage = '群聊不存在'
						break
					case 10016:
						errorMessage = '群聊已满员'
						break
					case 10037:
						errorMessage = '您已被禁止加入此群聊'
						break
					default:
						errorMessage = `加入群聊失败: ${response.message || '未知错误'}`
				}

				return { success: false, error: response, message: errorMessage }
			}
		} catch (error) {
			console.error('加入群组异常:', error)
			return { success: false, error: error, message: '加入群聊异常，请检查网络连接' }
		}
	}

	/**
	 * 退出群组
	 * @param {string} groupID - 群组ID
	 */
	async quitGroup(groupID) {
		try {
			if (!this.tim || !this.isLoggedIn) {
				throw new Error('IM 未登录')
			}

			const response = await this.tim.quitGroup(groupID)

			if (response.code === 0) {
				console.log('退出群组成功:', groupID)
				return { success: true, data: response.data }
			} else {
				console.error('退出群组失败:', response)
				return { success: false, error: response }
			}
		} catch (error) {
			console.error('退出群组异常:', error)
			return { success: false, error: error }
		}
	}

	/**
	 * 发送文本消息
	 * @param {string} groupID - 群组ID
	 * @param {string} text - 消息内容
	 */
	async sendTextMessage(groupID, text) {
		try {
			console.log('开始发送文本消息:', { groupID, text, isLoggedIn: this.isLoggedIn, isSDKReady: this.isSDKReady })

			if (!this.tim || !this.isLoggedIn || !this.isSDKReady) {
				throw new Error('IM 未登录或SDK未准备就绪')
			}

			// 创建文本消息
			console.log('创建文本消息...')
			const message = this.tim.createTextMessage({
				to: groupID,
				conversationType: TIM.TYPES.CONV_GROUP,
				payload: {
					text: text
				}
			})
			console.log('文本消息创建成功:', message)

			// 发送消息
			console.log('发送消息到服务器...')
			const response = await this.tim.sendMessage(message)
			console.log('发送消息响应:', response)

			if (response.code === 0) {
				console.log('发送消息成功，响应数据:', response.data)
				// 返回包含消息对象的数据结构
				return {
					success: true,
					data: {
						message: response.data.message || message,
						response: response.data
					}
				}
			} else {
				console.error('发送消息失败:', response)

				// 根据错误码提供用户友好的错误信息
				let errorMessage = '发送消息失败'
				switch(response.code) {
					case 10004:
						errorMessage = '您已被禁言，无法发送消息'
						break
					case 10007:
						errorMessage = '您没有权限发送消息'
						break
					case 10015:
						errorMessage = '群聊不存在'
						break
					case 10016:
						errorMessage = '群聊已解散'
						break
					case 80001:
						errorMessage = '消息内容包含敏感词汇'
						break
					case 80004:
						errorMessage = '消息发送频率过快，请稍后再试'
						break
					default:
						errorMessage = `发送消息失败: ${response.message || '未知错误'}`
				}

				return { success: false, error: response, message: errorMessage }
			}
		} catch (error) {
			console.error('发送消息异常:', error)
			return { success: false, error: error, message: '发送消息异常，请检查网络连接' }
		}
	}

	/**
	 * 获取群组消息列表
	 * @param {string} groupID - 群组ID
	 * @param {number} count - 消息数量，默认15条
	 */
	async getGroupMessageList(groupID, count = 15) {
		try {
			if (!this.tim || !this.isLoggedIn || !this.isSDKReady) {
				throw new Error('IM 未登录或SDK未准备就绪')
			}

			const response = await this.tim.getMessageList({
				conversationID: `GROUP${groupID}`,
				count: count
			})

			if (response.code === 0) {
				console.log('获取消息列表成功')
				return { success: true, data: response.data }
			} else {
				console.error('获取消息列表失败:', response)
				return { success: false, error: response }
			}
		} catch (error) {
			console.error('获取消息列表异常:', error)
			return { success: false, error: error }
		}
	}

	/**
	 * 获取会话列表
	 */
	async getConversationList() {
		try {
			if (!this.tim || !this.isLoggedIn || !this.isSDKReady) {
				throw new Error('IM 未登录或SDK未准备就绪')
			}

			const response = await this.tim.getConversationList()

			if (response.code === 0) {
				return { success: true, data: response.data }
			} else {
				return { success: false, error: response }
			}
		} catch (error) {
			return { success: false, error: error }
		}
	}

	/**
	 * 获取用户加入的群组列表
	 */
	async getJoinedGroupList() {
		try {
			if (!this.tim || !this.isLoggedIn || !this.isSDKReady) {
				throw new Error('IM 未登录或SDK未准备就绪')
			}

			// 使用腾讯云IM SDK的getGroupList方法获取用户加入的群组列表
			const response = await this.tim.getGroupList()

			if (response.code === 0) {
				console.log('获取用户加入的群组列表成功:', response.data)
				return { success: true, data: response.data }
			} else {
				console.error('获取用户加入的群组列表失败:', response)
				return { success: false, error: response }
			}
		} catch (error) {
			console.error('获取用户加入的群组列表异常:', error)
			return { success: false, error: error }
		}
	}

	/**
	 * 获取用户加入的群组列表和会话列表的交集
	 */
	async getJoinedGroupConversations() {
		try {
			if (!this.tim || !this.isLoggedIn || !this.isSDKReady) {
				throw new Error('IM 未登录或SDK未准备就绪')
			}

			// 并行获取用户加入的群组列表和会话列表
			const [joinedGroupsResult, conversationsResult] = await Promise.all([
				this.getJoinedGroupList(),
				this.getConversationList()
			])

			if (!joinedGroupsResult.success) {
				return { success: false, error: joinedGroupsResult.error, message: '获取加入群组列表失败' }
			}

			if (!conversationsResult.success) {
				return { success: false, error: conversationsResult.error, message: '获取会话列表失败' }
			}

			// 获取用户加入的群组ID列表
			const joinedGroupIds = (joinedGroupsResult.data.groupList || []).map(group => group.groupID)
			console.log('用户加入的群组ID列表:', joinedGroupIds)

			// 过滤出群聊会话
			const groupConversations = (conversationsResult.data.conversationList || []).filter(conversation =>
				conversation.type === 'GROUP'
			)
			console.log('所有群聊会话:', groupConversations.map(c => c.conversationID))

			// 取交集：只保留用户已加入的群组的会话
			const intersectionConversations = groupConversations.filter(conversation => {
				const groupId = conversation.conversationID.replace('GROUP', '')
				return joinedGroupIds.includes(groupId)
			})

			console.log('交集群聊会话:', intersectionConversations.map(c => c.conversationID))

			// 处理每个群聊的未读消息数，过滤掉系统消息
			const processedConversations = intersectionConversations.map(conversation => {
				let unreadCount = conversation.unreadCount || 0

				// 如果有最后一条消息，检查是否为有效的用户消息
				if (conversation.lastMessage) {
					const lastMessage = conversation.lastMessage

					// 过滤掉系统消息和群提示消息
					if (lastMessage.type === 'TIMGroupTipElem' ||
						lastMessage.type === 'TIMGroupSystemNoticeElem' ||
						(lastMessage.payload && lastMessage.payload.operationType)) {
						// 如果最后一条消息是系统消息，减少未读数
						unreadCount = Math.max(0, unreadCount - 1)
					}
				}

				// 更新会话的未读数
				return {
					...conversation,
					unreadCount: unreadCount
				}
			})

			return {
				success: true,
				data: {
					conversationList: processedConversations,
					joinedGroupCount: joinedGroupIds.length,
					conversationCount: processedConversations.length
				}
			}

		} catch (error) {
			console.error('获取群组会话交集异常:', error)
			return { success: false, error: error, message: '获取群聊列表异常，请检查网络连接' }
		}
	}

	/**
	 * 获取总未读消息数
	 */
	async getTotalUnreadCount() {
		try {
			if (!this.tim || !this.isLoggedIn || !this.isSDKReady) {
				return { success: false, error: 'IM 未登录或SDK未准备就绪', count: 0 }
			}
			const res = this.tim.getTotalUnreadMessageCount()
			return res
			return console.log("@@@@@@@@@@@@@@", res);
			
			const response = await this.getConversationList()
			if (response.success) {
				const conversations = response.data.conversationList || []

				// 只计算群聊会话的未读消息数
				const groupConversations = conversations.filter(conversation =>
					conversation.type === 'GROUP'
				)

				// 计算有效的未读消息数（排除系统消息等）
				let totalUnread = 0
				const validConversations = []

				for (const conversation of groupConversations) {
					// 获取会话的未读消息数
					let unreadCount = conversation.unreadCount || 0

					// 如果有最后一条消息，检查是否为有效的用户消息
					if (conversation.lastMessage) {
						const lastMessage = conversation.lastMessage

						// 过滤掉系统消息和群提示消息
						if (lastMessage.type === 'TIMGroupTipElem' ||
							lastMessage.type === 'TIMGroupSystemNoticeElem' ||
							(lastMessage.payload && lastMessage.payload.operationType)) {
							// 如果最后一条消息是系统消息，减少未读数
							unreadCount = Math.max(0, unreadCount - 1)
						}
					}

					totalUnread += unreadCount

					// 更新会话的未读数
					conversation.unreadCount = unreadCount
					validConversations.push(conversation)
				}

				console.log('总未读消息数:', totalUnread, '群聊数量:', groupConversations.length)
				return { success: true, count: totalUnread, conversations: validConversations }
			} else {
				return { success: false, error: response.error, count: 0 }
			}
		} catch (error) {
			console.error('获取未读消息数异常:', error)
			return { success: false, error: error, count: 0 }
		}
	}

	/**
	 * 标记会话为已读
	 * @param {string} conversationID - 会话ID，格式如 'GROUP123456'
	 */
	async markConversationAsRead(conversationID) {
		try {
			if (!this.tim || !this.isLoggedIn || !this.isSDKReady) {
				return { success: false, error: 'IM 未登录或SDK未准备就绪' }
			}

			const response = await this.tim.setMessageRead({
				conversationID: conversationID
			})

			if (response.code === 0) {
				console.log('标记会话已读成功:', conversationID)
				return { success: true, data: response.data }
			} else {
				console.error('标记会话已读失败:', response)
				return { success: false, error: response }
			}
		} catch (error) {
			console.error('标记会话已读异常:', error)
			return { success: false, error: error }
		}
	}


	/**
	 * 注册群组更新回调
	 * @param {Function} callback - 回调函数
	 */
	onGroupUpdated(callback) {
		if (typeof callback === 'function') {
			this.groupCallbacks.push(callback)
		}
	}

	/**
	 * 添加群组回调（别名方法，与onGroupUpdated功能相同）
	 * @param {Function} callback - 回调函数
	 */
	addGroupCallback(callback) {
		if (typeof callback === 'function') {
			this.groupCallbacks.push(callback)
			console.log('添加群组回调成功，当前回调数量:', this.groupCallbacks.length)
		} else {
			console.warn('addGroupCallback: 回调函数无效')
		}
	}

	/**
	 * 移除群组回调
	 * @param {Function} callback - 回调函数
	 */
	removeGroupCallback(callback) {
		const index = this.groupCallbacks.indexOf(callback)
		if (index > -1) {
			this.groupCallbacks.splice(index, 1)
			console.log('移除群组回调成功，当前回调数量:', this.groupCallbacks.length)
		} else {
			console.warn('removeGroupCallback: 未找到指定的回调函数')
		}
	}

	/**
	 * 移除消息接收回调
	 * @param {Function} callback - 回调函数
	 */
	offMessageReceived(callback) {
		const index = this.messageCallbacks.indexOf(callback)
		if (index > -1) {
			this.messageCallbacks.splice(index, 1)
		}
	}

	/**
	 * 移除群组更新回调（别名方法，与removeGroupCallback功能相同）
	 * @param {Function} callback - 回调函数
	 */
	offGroupUpdated(callback) {
		this.removeGroupCallback(callback)
	}

	/**
	 * 获取当前登录状态
	 */
	getLoginStatus() {
		return {
			isLoggedIn: this.isLoggedIn,
			currentUserID: this.currentUserID
		}
	}

	/**
	 * 注册登录成功回调
	 * @param {Function} callback - 回调函数
	 */
	onLoginSuccess(callback) {
		if (typeof callback === 'function') {
			this.loginSuccessCallbacks.push(callback)
		}
	}

	/**
	 * 移除登录成功回调
	 * @param {Function} callback - 回调函数
	 */
	offLoginSuccess(callback) {
		const index = this.loginSuccessCallbacks.indexOf(callback)
		if (index > -1) {
			this.loginSuccessCallbacks.splice(index, 1)
		}
	}

	/**
	 * 触发登录成功回调
	 */
	triggerLoginSuccessCallbacks() {
		this.loginSuccessCallbacks.forEach(callback => {
			try {
				if (typeof callback === 'function') {
					callback()
				}
			} catch (error) {
				console.error('登录成功回调执行失败:', error)
			}
		})
	}

	/**
	 * 注册SDK ready回调
	 * @param {Function} callback - 回调函数
	 */
	onReady(callback) {
		if (typeof callback === 'function') {
			this.readyCallbacks.push(callback)

			// 如果SDK已经ready，立即执行回调
			if (this.isSDKReady) {
				try {
					callback()
				} catch (error) {
					console.error('SDK ready回调执行失败:', error)
				}
			}
		}
	}

	/**
	 * 移除SDK ready回调
	 * @param {Function} callback - 回调函数
	 */
	offReady(callback) {
		const index = this.readyCallbacks.indexOf(callback)
		if (index > -1) {
			this.readyCallbacks.splice(index, 1)
		}
	}

	/**
	 * 触发SDK ready回调
	 */
	triggerReadyCallbacks() {
		this.readyCallbacks.forEach(callback => {
			try {
				if (typeof callback === 'function') {
					callback()
				}
			} catch (error) {
				console.error('SDK ready回调执行失败:', error)
			}
		})
	}

	/**
	 * 清理IM状态（用于退出登录时）
	 */
	async cleanup() {
		try {
			// 先登出IM
			if (this.tim && this.isLoggedIn) {
				await this.logout()
			}

			// 销毁TIM实例
			if (this.tim) {
				try {
					this.tim.destroy()
					console.log('TIM实例已销毁')
				} catch (destroyError) {
					console.warn('销毁TIM实例时出错:', destroyError)
				}
				this.tim = null
			}

			// 清理回调函数
			this.messageCallbacks = []
			this.groupCallbacks = []
			this.loginSuccessCallbacks = []
			this.readyCallbacks = []

			// 重置状态
			this.isLoggedIn = false
			this.isSDKReady = false
			this.currentUserID = ''

			console.log('IM状态已清理')
		} catch (error) {
			console.error('清理IM状态失败:', error)
		}
	}

	/**
	 * 测试发送消息功能
	 */
	async testSendMessage(groupID, text = '测试消息') {
		console.log('=== 开始测试发送消息功能 ===')
		console.log('参数:', { groupID, text })

		try {
			// 检查登录状态
			console.log('检查登录状态...')
			const loginStatus = this.getLoginStatus()
			console.log('登录状态:', loginStatus)

			if (!loginStatus.isLoggedIn) {
				return { success: false, error: 'IM未登录' }
			}

			// 检查TIM实例
			if (!this.tim) {
				return { success: false, error: 'TIM实例不存在' }
			}

			// 创建消息
			console.log('创建测试消息...')
			const message = this.tim.createTextMessage({
				to: groupID,
				conversationType: TIM.TYPES.CONV_GROUP,
				payload: { text: text }
			})
			console.log('消息创建结果:', message)

			// 发送消息
			console.log('发送测试消息...')
			const response = await this.tim.sendMessage(message)
			console.log('发送响应:', response)

			return {
				success: response.code === 0,
				data: response,
				message: response.code === 0 ? '发送成功' : `发送失败: ${response.message}`
			}
		} catch (error) {
			console.error('测试发送消息异常:', error)
			return { success: false, error: error.message }
		}
	}
}

// 创建单例实例
const imManager = new IMManager()

export default imManager
